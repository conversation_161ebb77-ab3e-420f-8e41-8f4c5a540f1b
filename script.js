let move_speed = 3, grativy = 0.5;
let bird = document.querySelector('.bird');
let img = document.getElementById('bird-1');
let sound_point = new Audio('sounds effect/point.mp3');
let sound_die = new Audio('sounds effect/die.mp3');
let sound_reward = new Audio('sounds effect/Reward unlocked.wav');

let sound_background = new Audio('sounds effect/background.mp3');
sound_background.loop = true;
sound_background.volume = 0.5;

let bird_props = bird.getBoundingClientRect();
let background = document.querySelector('.background').getBoundingClientRect();
let score_val = document.querySelector('.score_val');
let message = document.querySelector('.message');
let score_title = document.querySelector('.score_title');

let game_state = 'Start';
let pipes_created = 0; // Track total number of pipes created
let max_pipes = 10; // Maximum number of pipes allowed
img.style.display = 'none';
message.classList.add('messageStyle');

// Start background music on Enter
document.addEventListener('keydown', (e) => {
    if (e.key == 'Enter' && game_state != 'Play') {
        sound_background.play();
    }
});

document.addEventListener('keydown', (e) => {
    if (e.key == 'Enter' && game_state != 'Play') {
        document.querySelectorAll('.pipe_sprite').forEach((e) => e.remove());
        img.style.display = 'block';
        bird.style.top = '40vh';
        game_state = 'Play';
        message.innerHTML = '';
        score_title.innerHTML = 'Score : ';
        score_val.innerHTML = '0';
        message.classList.remove('messageStyle');
        // Reset pipe counter when game starts
        pipes_created = 0;
        play();
    }
});

function play() {
    // Store event listener references so we can remove them later
    let keyDownHandler, keyUpHandler;
    
    function move() {
        if (game_state != 'Play') return;

        let pipe_sprite = document.querySelectorAll('.pipe_sprite');
        pipe_sprite.forEach((element) => {
            let pipe_sprite_props = element.getBoundingClientRect();
            bird_props = bird.getBoundingClientRect();

            if (pipe_sprite_props.right <= 0) {
                element.remove();
            } else {
                // Collision detection
                if (
                    bird_props.left < pipe_sprite_props.left + pipe_sprite_props.width &&
                    bird_props.left + bird_props.width > pipe_sprite_props.left &&
                    bird_props.top < pipe_sprite_props.top + pipe_sprite_props.height &&
                    bird_props.top + bird_props.height > pipe_sprite_props.top
                ) {
                    game_state = 'End';
                    message.innerHTML = 'Game Over'.fontcolor('red') + '<br>Press Enter To Restart';
                    message.classList.add('messageStyle');
                    img.style.display = 'none';
                    sound_die.play();
                    return;
                } else {
                    // Score update and victory check
                    if (
                        pipe_sprite_props.right < bird_props.left &&
                        pipe_sprite_props.right + move_speed >= bird_props.left &&
                        element.increase_score == '1'
                    ) {
                        let currentScore = parseInt(score_val.innerHTML);
                        currentScore += 1;
                        score_val.innerHTML = currentScore;
                        element.increase_score = '0';
                        sound_point.play();

                        // Show congratulatory message at score 10 with options
                        if (currentScore === 10) {
                            // Pause the game
                            game_state = 'Paused';

                            // Play reward sound
                            sound_reward.play();

                            // Create a popup overlay with options
                            let overlay = document.createElement('div');
                            overlay.className = 'victory-popup-overlay';
                            overlay.innerHTML = `
                                <div class="victory-popup">
                                    <div class="victory-content">
                                        <div class="victory-text">
                                            🎉 <strong>Congrats! You have completed the game.</strong>
                                        </div>
                                        <div class="reward-text">
                                            🎁 <strong>Here's your reward!</strong> 🎁
                                        </div>
                                        <div class="trophy-container">
                                            <div class="trophy-box">
                                                <img src="images/IMG_3184.gif" alt="Reward" class="reward-gif">
                                            </div>
                                        </div>
                                        <div class="victory-buttons">
                                            <button id="exitBtn" class="victory-btn exit-btn">RESTART</button>
                                            <button id="continueBtn" class="victory-btn continue-btn">NEXT</button>
                                        </div>
                                    </div>
                                </div>
                            `;
                            document.body.appendChild(overlay);
                            
                            // Add event listeners to buttons
                            setTimeout(() => {
                                document.getElementById('continueBtn').addEventListener('click', function() {
                                    document.body.removeChild(overlay);
                                    
                                    // Create countdown overlay
                                    let countdownOverlay = document.createElement('div');
                                    countdownOverlay.style.position = 'fixed';
                                    countdownOverlay.style.top = '50%';
                                    countdownOverlay.style.left = '50%';
                                    countdownOverlay.style.transform = 'translate(-50%, -50%)';
                                    countdownOverlay.style.background = 'rgba(0, 0, 0, 0.7)';
                                    countdownOverlay.style.color = 'white';
                                    countdownOverlay.style.fontSize = '5em';
                                    countdownOverlay.style.padding = '50px';
                                    countdownOverlay.style.borderRadius = '20px';
                                    countdownOverlay.style.zIndex = '1000';
                                    countdownOverlay.style.textAlign = 'center';
                                    document.body.appendChild(countdownOverlay);
                                    
                                    // Start countdown from 3
                                    let count = 3;
                                    countdownOverlay.textContent = count;
                                    
                                    let countdownInterval = setInterval(() => {
                                        count--;
                                        if (count > 0) {
                                            countdownOverlay.textContent = count;
                                        } else {
                                            clearInterval(countdownInterval);
                                            document.body.removeChild(countdownOverlay);
                                            
                                            // Save current score
                                            let currentScore = parseInt(score_val.innerHTML);
                                            
                                            // Reset game state but preserve score
                                            document.querySelectorAll('.pipe_sprite').forEach((e) => e.remove());
                                            bird.style.top = '40vh';
                                            game_state = 'Play';
                                            score_val.innerHTML = currentScore;
                                        }
                                    }, 1000);
                                });
                                
                                document.getElementById('exitBtn').addEventListener('click', function() {
                                    document.body.removeChild(overlay);
                                    // End the game and show restart message
                                    game_state = 'End';
                                    message.innerHTML = 'Game Over'.fontcolor('red') + '<br>Press Enter To Restart';
                                    message.classList.add('messageStyle');
                                    img.style.display = 'none';
                                });
                            }, 100); // Small timeout to ensure DOM elements are available
                        }
                    }
                    element.style.left = pipe_sprite_props.left - move_speed + 'px';
                }
            }
        });
        requestAnimationFrame(move);
    }
    requestAnimationFrame(move);

    let bird_dy = 0;

    function apply_gravity() {
        if (game_state != 'Play') return;

        bird_dy = bird_dy + grativy;

        // Remove existing event listeners if they exist
        if (keyDownHandler) document.removeEventListener('keydown', keyDownHandler);
        if (keyUpHandler) document.removeEventListener('keyup', keyUpHandler);
        
        // Create new event listeners
        keyDownHandler = (e) => {
            if (e.key == 'ArrowUp' || e.key == ' ') {
                img.src = 'images/flappybird-01.png';
                bird_dy = -7.6;
            }
        };
        
        keyUpHandler = (e) => {
            if (e.key == 'ArrowUp' || e.key == ' ') {
                img.src = 'images/flappybird-02.png';
            }
        };
        
        // Add the event listeners
        document.addEventListener('keydown', keyDownHandler);
        document.addEventListener('keyup', keyUpHandler);

        if (bird_props.top <= 0 || bird_props.bottom >= background.bottom) {
            game_state = 'End';
            message.style.left = '28vw';
            window.location.reload();
            message.classList.remove('messageStyle');
            return;
        }

        bird.style.top = bird_props.top + bird_dy + 'px';
        bird_props = bird.getBoundingClientRect();
        requestAnimationFrame(apply_gravity);
    }
    requestAnimationFrame(apply_gravity);

    let pipe_seperation = 0;
    let pipe_gap = 35;

    function create_pipe() {
        if (game_state != 'Play') return;

        // Stop creating new pipes after 10 pipes have been created
        if (pipes_created >= max_pipes) return;

        if (pipe_seperation > 115) {
            pipe_seperation = 0;

            let pipe_posi = Math.floor(Math.random() * 43) + 8;

            // Create upper pipe
            let pipe_sprite_upper = document.createElement('div');
            pipe_sprite_upper.className = 'pipe_sprite';
            pipe_sprite_upper.style.top = '0vh';
            pipe_sprite_upper.style.height = pipe_posi + 'vh';
            pipe_sprite_upper.style.left = '100vw';
            document.body.appendChild(pipe_sprite_upper);

            // Create lower pipe (existing code)
            let pipe_sprite = document.createElement('div');
            pipe_sprite.className = 'pipe_sprite';
            pipe_sprite.style.top = pipe_posi + pipe_gap + 'vh';
            pipe_sprite.style.left = '100vw';
            pipe_sprite.increase_score = '1';
            document.body.appendChild(pipe_sprite);

            // Increment the pipe counter
            pipes_created++;
        }

        pipe_seperation++;
        requestAnimationFrame(create_pipe);
    }

    requestAnimationFrame(create_pipe);
}

// Reset and prepare music
sound_background.loop = true;
sound_background.volume = 0.5;
sound_background.play();
sound_background.pause();
sound_background.currentTime = 0;












