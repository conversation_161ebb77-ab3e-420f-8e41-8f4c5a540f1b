html, body {
    height: 100%;
    width: 100%;
}

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
	font-family: 'Press Start 2P', cursive;
}
.background {
	height: 100vh;
	width: 100vw;
	background: url('images/bg\ pic-01.png') no-repeat center center fixed;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
}
.bird {
	height: 80px;
	width: 80px;
	position: fixed;
	top: 40vh;
	left: 30vw;
	z-index: 100;
}
.pipe_sprite {
	position: fixed;
	top: 20vh; 
	left: 60vw;
	height: 70vh;
	width: 6vw;
	background:radial-gradient(lightgreen 50%, green);
	border: 5px solid black;
}
.message {
	position: absolute;
	z-index: 10;
	color: black;
	top: 30%;
	left: 50%;
	font-size: 2.5em; /* Increased from 2em to 2.5em */
	transform: translate(-50%, -50%);
	text-align: center;
}
.messageStyle{
	background: white;
	padding: 30px;
	box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
	border-radius: 5%;
}
.score {
	position: fixed;
	z-index: 10;
	height: 10vh;
	font-size: 8vh; /* Reduced from 10vh to 8vh */
	font-weight: 100;
	color: white;
	-webkit-text-stroke-width: 2px;
    -webkit-text-stroke-color: black;
	top: 0;
	left: 0;
	margin: 10px;
	font-family: 'Press Start 2P', cursive;
}
.score_val {
	color: gold;
	font-weight: bold;
}
/* Victory Popup Styles */
.victory-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.victory-popup {
    background: white;
    border: 3px solid #333;
    border-radius: 15px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    text-align: center;
    font-family: 'Press Start 2P', cursive;
}

.victory-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.victory-text-horizontal {
    font-size: 16px;
    color: #333;
    line-height: 1.2;
    white-space: nowrap;
    text-align: center;
}

.reward-text-horizontal {
    font-size: 14px;
    color: #333;
    line-height: 1.2;
    white-space: nowrap;
    text-align: center;
}

.trophy-container {
    margin: 10px 0;
}

.trophy-box {
    display: inline-block;
    background: #fff;
    border: 4px solid #FFD700;
    border-radius: 10px;
    padding: 10px;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    overflow: hidden;
}

.reward-gif {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 6px;
}

.victory-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.victory-btn {
    font-family: 'Press Start 2P', cursive;
    font-size: 12px;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
}

.continue-btn {
    background: #4CAF50;
    color: white;
}

.continue-btn:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.exit-btn {
    background: #f44336;
    color: white;
}

.exit-btn:hover {
    background: #da190b;
    transform: translateY(-2px);
}

@media only screen and (max-width: 1080px) {
    .message{
		font-size: 50px;
		top: 50%;
		white-space: nowrap;
	}
	.score{
		font-size: 8vh;
	}
	.bird{
		width: 60px;
		height: 40px;
	}
	.pipe_sprite{
		width: 4vw;
	}

	.victory-popup {
        padding: 20px;
        margin: 10px;
    }

    .victory-text {
        font-size: 12px;
    }

    .reward-text {
        font-size: 10px;
    }

    .trophy-box {
        width: 100px;
        height: 100px;
        padding: 8px;
    }

    .victory-btn {
        font-size: 10px;
        padding: 10px 12px;
        min-width: 80px;
        flex: 1;
        max-width: 120px;
    }

    .victory-buttons {
        flex-direction: row;
        justify-content: center;
        gap: 10px;
    }
}




